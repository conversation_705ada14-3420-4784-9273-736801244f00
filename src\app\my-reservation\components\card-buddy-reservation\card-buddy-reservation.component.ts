import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from "@angular/core";
import { MatAutocompleteSelectedEvent } from "@angular/material/autocomplete";
import {
  MatBottomSheetRef,
  MatBottomSheet,
} from "@angular/material/bottom-sheet";
import { BuddyListStatus, Buddy } from "../../../teetime/models/buddy.model";
import { Golfer } from "../../../teetime/models/golfer.model";
import { Player } from "../../../teetime/models/player.model";

@Component({
  selector: "app-card-buddy-reservation",
  templateUrl: "./card-buddy-reservation.component.html",
  styleUrls: ["./card-buddy-reservation.component.scss"],
})
export class CardBuddyReservationComponent implements OnInit {
  buddyStatus: BuddyListStatus;
  buddyListAddOrRemove: BuddyListStatus[] = [];

  @ViewChild("filter") filter: ElementRef;
  @Input() isDisplayStore: boolean;
  @Input() isDisable: boolean;
  @Input() isShowBuddiesLoadMore: boolean;
  @Input() buddiesPage: Buddy[];
  @Input() filteredBuddies: Golfer[];
  @Output() searchBuddies = new EventEmitter<string>();
  @Output() selectBuddy = new EventEmitter<Golfer>();
  @Output() addBuddy = new EventEmitter<Golfer>();
  @Output() removeBuddy = new EventEmitter<Golfer>();
  @Output() addPlayer = new EventEmitter<Player>();
  @Output() loadMoreBuddies = new EventEmitter();

  ngOnInit(): void {
    for (let index = 0; index < this.buddiesPage.length; index++) {
      this.buddyStatus = {
        buddyId: this.buddiesPage[index].buddyId,
        acct: this.buddiesPage[index].acct,
        golferId: this.buddiesPage[index].golferId,
        isAdd: false,
      };
      this.buddyListAddOrRemove.push(this.buddyStatus);
    }
  }

  onSelectSearchBuddy(event: MatAutocompleteSelectedEvent) {
    this.selectBuddy.emit(event.option.value);
    this.filter.nativeElement.value = null;
  }

  displayFn(golfer?: Buddy): string | undefined {
    return golfer ? golfer.firstName + " " + golfer.lastName : undefined;
  }

  trackByFn(index: number, item: Golfer) {
    return item.buddyId;
  }

  updateBuddyStatus(buddy: any) {
    if (this.buddyListAddOrRemove.length > 0) {
      if (
        this.buddyListAddOrRemove.find(
          (x) => x.buddyId === buddy.buddyId && x.isAdd
        )
      ) {
        const partnerIndex: number = this.buddyListAddOrRemove.findIndex(
          (p) => p.buddyId === buddy.buddyId
        );
        this.buddyListAddOrRemove[partnerIndex].isAdd = false;
      } else if (
        this.buddyListAddOrRemove.find(
          (x) => x.buddyId === buddy.buddyId && !x.isAdd
        )
      ) {
        const partnerIndex: number = this.buddyListAddOrRemove.findIndex(
          (p) => p.buddyId === buddy.buddyId
        );
        this.buddyListAddOrRemove[partnerIndex].isAdd = true;
      } else {
        this.buddyStatus = {
          buddyId: buddy.buddyId,
          acct: buddy.acct,
          golferId: buddy.golferId,
          isAdd: true,
        };
        this.buddyListAddOrRemove.push(this.buddyStatus);
      }
    } else {
      this.buddyStatus = {
        buddyId: buddy.buddyId,
        acct: buddy.acct,
        golferId: buddy.golferId,
        isAdd: true,
      };
      this.buddyListAddOrRemove.push(this.buddyStatus);
    }
  }

  onClickAddBuddy(golfer: any) {
    this.updateBuddyStatus(golfer);
    this.addBuddy.emit(golfer);
    this.addPlayer.emit(golfer);
  }

  onClickRemoveBuddy(golfer: any) {
    this.updateBuddyStatus(golfer);
    this.removeBuddy.emit(golfer);
    this.addPlayer.emit(golfer);
  }
}
