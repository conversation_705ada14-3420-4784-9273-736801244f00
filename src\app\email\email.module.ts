import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { EmailRoutingModule } from "./email-routing.module";
import { FormsModule } from "@angular/forms";
import { EffectsModule } from "@ngrx/effects";
import { TranslateModule } from "@ngx-translate/core";
import { UnsubscribeEffects } from "./effects/unsubscribe.effects";
import { SharedModule } from "../shared/shared.module";
import { StoreModule } from "@ngrx/store";
import { reducers } from "./reducers";

@NgModule({
  imports: [
    SharedModule,
    CommonModule,
    EmailRoutingModule,
    FormsModule,
    StoreModule.forFeature("unsubscribe", reducers),
    EffectsModule.forFeature([
      UnsubscribeEffects
    ]),
    TranslateModule,
  ],
  declarations: [EmailRoutingModule.components],
})
export class EmailModule {}
