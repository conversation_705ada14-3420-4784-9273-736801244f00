import { ValueObject } from "../../shared-kernal/primitives/value-object";

export interface WelcomeScreenBanner {
  imageSrc: string;
  alternateText: string;
}

export class WelcomeScreenBannerObject extends ValueObject<WelcomeScreenBanner> {
  static readonly defaultValue = {
    imageSrc: null,
    alternateText: '',
  } as const;

  public static create(
    value: WelcomeScreenBanner,
    defaultValue: Partial<WelcomeScreenBanner> = null
  ): WelcomeScreenBanner {
    if (defaultValue && !value) {
      return { ...defaultValue } as WelcomeScreenBanner;
    }

    const obj = new WelcomeScreenBannerObject(
      value,
      defaultValue ?? this.defaultValue
    );
    return obj.value;
  }
}
