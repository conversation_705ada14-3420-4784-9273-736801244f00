import { ValueObject } from "../../shared-kernal/primitives/value-object";

export interface MonerisTokenResponse {
  isSuccess: boolean;
  message: string;
}

export class MonerisTokenResponseObject extends ValueObject<MonerisTokenResponse> {
  static readonly defaultValue = {
    message: "",
    isSuccess: false,
  } as const;

  public static create(
    value: MonerisTokenResponse,
    defaultValue: Partial<MonerisTokenResponse> = null
  ): MonerisTokenResponse {
    if (defaultValue && !value) {
      return { ...defaultValue } as MonerisTokenResponse;
    }

    const obj = new MonerisTokenResponseObject(
      value,
      defaultValue ?? this.defaultValue
    );
    return obj.value;
  }
}
