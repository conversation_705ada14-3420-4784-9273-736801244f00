import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from "@angular/core";
import { QueryParamType } from "../../../mainshared/constants/QueryParamType";
import { Helper } from "../../../shared/utilities/helper";
import { select, Store } from "@ngrx/store";
import { Unsubscribe } from "../../models/unsubscribe.model";
import { DomSanitizer, SafeHtml } from "@angular/platform-browser";
import * as UnsubscribePageActions from "../../actions/unsubscribe.actions";
import * as fromUnsubscribe from "../../reducers";
import { takeWhile } from "rxjs";

@Component({
  selector: "app-unsubscribe-page",
  templateUrl: "./unsubscribe-page.component.html",
  styleUrls: ["./unsubscribe-page.component.scss"],
})
export class UnsubscribePageComponent implements OnInit, OnDestroy {
  email: string;
  privacyMessageSanitizedHtml: SafeHtml;
  isReservationEmail = false;
  isMarketingEmail = false;

  private isComponentAlive = true;
  private key: string;
  private payload: Unsubscribe;

  constructor(
    private store: Store<fromUnsubscribe.State>,
    private domSanitizer: DomSanitizer
  ) {
    this.store
      .pipe(
        select(fromUnsubscribe.selectIsReservationEmail),
        takeWhile(() => this.isComponentAlive)
      )
      .subscribe((isReservationEmail) => {
        this.isReservationEmail = isReservationEmail;
      });

    this.store
      .pipe(
        select(fromUnsubscribe.selectIsMarketingEmail),
        takeWhile(() => this.isComponentAlive)
      )
      .subscribe((isMarketingEmail) => {
        this.isMarketingEmail = isMarketingEmail;
      });

    this.store
      .pipe(
        select(fromUnsubscribe.selectPrivacyPolicy),
        takeWhile(() => this.isComponentAlive)
      )
      .subscribe((privacyPolicy) => {
        this.privacyMessageSanitizedHtml = privacyPolicy
          ? this.domSanitizer.bypassSecurityTrustHtml(privacyPolicy)
          : "";
      });
  }

  ngOnInit(): void {
    const url = decodeURIComponent(window.location.href);
    this.key = Helper.getQueryParamFromMalformedURL(url, QueryParamType.Key);
    const decodedKey = Helper.decodeBase64(this.key);
    this.payload = JSON.parse(decodedKey);
    this.email = this.payload.email;

    if (!this.key) return;

    this.store.dispatch(
      new UnsubscribePageActions.FetchUnsubscribeInformation({
        key: this.key,
      })
    );
  }

  ngOnDestroy(): void {
    this.isComponentAlive = false;
    this.store.dispatch(new UnsubscribePageActions.ResetUnsubscribe());
  }

  onClickUnsubscribe() {
    this.store.dispatch(
      new UnsubscribePageActions.Unsubscribe({
        key: this.key,
        isReservationEmail: this.isReservationEmail,
        isMarketingEmail: this.isMarketingEmail,
      })
    );
  }
}
