import { Action } from "@ngrx/store";
import { Unsubscribe as UnsubscribeModel } from "../models/unsubscribe.model";

export enum UnsubscribeActionTypes {
  FetchUnsubscribeInformation = "[UnsubscribePage] FetchUnsubscribeInformation",
  FetchUnsubscribeInformationSuccess = "[UnsubscribePage] FetchUnsubscribeInformationSuccess",
  FetchUnsubscribeInformationFailed = "[UnsubscribePage] FetchUnsubscribeInformationFailed",
  Unsubscribe = "[UnsubscribePage] Unsubscribe",
  UnsubscribeSuccess = "[UnsubscribePage] UnsubscribeSuccess",
  UnsubscribeFailed = "[UnsubscribePage] UnsubscribeFailed",
  ResetUnsubscribe = "[UnsubscribePage] ResetUnsubscribe",
}

export class FetchUnsubscribeInformation implements Action {
  readonly type = UnsubscribeActionTypes.FetchUnsubscribeInformation;

  constructor(public payload: { key: string }) {}
}

export class FetchUnsubscribeInformationSuccess implements Action {
  readonly type = UnsubscribeActionTypes.FetchUnsubscribeInformationSuccess;

  constructor(public payload: any) {}
}

export class FetchUnsubscribeInformationFailed implements Action {
  readonly type = UnsubscribeActionTypes.FetchUnsubscribeInformationFailed;

  constructor(public payload: any) {}
}

export class Unsubscribe implements Action {
  readonly type = UnsubscribeActionTypes.Unsubscribe;

  constructor(public payload: Partial<UnsubscribeModel>) {}
}

export class UnsubscribeSuccess implements Action {
  readonly type = UnsubscribeActionTypes.UnsubscribeSuccess;

  constructor(public payload: any) {}
}

export class UnsubscribeFailed implements Action {
  readonly type = UnsubscribeActionTypes.UnsubscribeFailed;

  constructor(public payload: any) {}
}

export class ResetUnsubscribe implements Action {
  readonly type = UnsubscribeActionTypes.ResetUnsubscribe;
}

export type UnsubscribePageActionsUnion =
  | FetchUnsubscribeInformation
  | FetchUnsubscribeInformationSuccess
  | FetchUnsubscribeInformationFailed
  | Unsubscribe
  | UnsubscribeSuccess
  | UnsubscribeFailed
  | ResetUnsubscribe;
