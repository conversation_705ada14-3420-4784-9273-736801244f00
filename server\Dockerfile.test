ARG NODE_IMAGE=node:18.20.8

FROM ${NODE_IMAGE} AS node-build
WORKDIR /web

# Update system packages and fix curl vulnerability
RUN apt-get update && \
    apt-get install -y \
    curl \
    libcurl4 \
    libcurl4-openssl-dev \
    wget && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

COPY package*.json ./
RUN npm i -g npm@10.8.1 && \
    npm ci --legacy-peer-deps
COPY . .

# RUN npm install -g @angular/cli@6.0.5
ARG NODE_ENV=prod
ENV NODE_ENV=$NODE_ENV
## Build the angular app in production mode and store the artifacts in dist folder
RUN echo "NODE_ENV: ${NODE_ENV}" && \
    npm run lint && \
    npm run build:"$NODE_ENV" && npm run post-build
