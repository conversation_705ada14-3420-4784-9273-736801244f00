import { Component, EventEmitter, Input, Output } from "@angular/core";
import { Page } from "../../../shared/models/page.model";
import { Player } from "../../../teetime/models/player.model";

@Component({
  selector: "app-card-partner-reservation",
  templateUrl: "./card-partner-reservation.component.html",
  styleUrls: ["./card-partner-reservation.component.scss"],
})
export class CardPartnerReservationComponent {
  @Input() partnerPage: Page<Player>;
  @Input() isDisplayStore: boolean;
  @Input() isDisable: boolean;
  @Output() loadMorePartners = new EventEmitter();
  @Output() addPartnerPlayer = new EventEmitter<Player>();

  trackByFn(index: number, item: Player) {
    return item.golferId;
  }
}
