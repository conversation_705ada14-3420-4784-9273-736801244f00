import { UnsubscribePageActions } from "../actions";
import { UnsubscribePageActionsUnion } from "../actions/unsubscribe.actions";

export interface State {
  privacyPolicy: string | null;
  isReservationEmail: boolean;
  isMarketingEmail: boolean;
}

const initialState: State = {
  privacyPolicy: null,
  isReservationEmail: false,
  isMarketingEmail: false,
};

export function reducer(
  state = initialState,
  action: UnsubscribePageActions.UnsubscribePageActionsUnion
): State {
  switch (action.type) {
    case UnsubscribePageActions.UnsubscribeActionTypes
      .FetchUnsubscribeInformationSuccess: {
      const { privacyPolicy, isReservationEmail, isMarketingEmail } =
        action.payload;
      return {
        ...state,
        privacyPolicy: privacyPolicy,
        isReservationEmail: isReservationEmail,
        isMarketingEmail: isMarketingEmail,
      };
    }
    case UnsubscribePageActions.UnsubscribeActionTypes.ResetUnsubscribe: {
      return initialState;
    }
    default: {
      return state;
    }
  }
}

export const getPrivacyPolicy = (state: State) => state.privacyPolicy;
export const getIsReservationEmail = (state: State) => state.isReservationEmail;
export const getIsMarketingEmail = (state: State) => state.isMarketingEmail;
