import { Injectable } from "@angular/core";
import { HttpClient, HttpParams } from "@angular/common/http";
import { environment } from "../../../environments/environment";
import { BaseConfigurationService } from "../../core/services/base-configuration.service";
import { map } from "rxjs/operators";
import { Unsubscribe } from "../../email/models/unsubscribe.model";

@Injectable({ providedIn: "root" })
export class UnsubscribeService {
  private baseUrl = environment.emailApi;

  constructor(
    private httpClient: HttpClient,
    private baseConfigService: BaseConfigurationService
  ) {
    // Load service base url from server environment variables
    if (this.baseConfigService.isReady) {
      this.baseUrl = this.baseConfigService.serverSettings.emailApi;
    } else {
      this.baseConfigService.settingsLoaded$.subscribe(
        (x) => (this.baseUrl = this.baseConfigService.serverSettings.emailApi)
      );
    }
  }

  unsubscribe(model: Partial<Unsubscribe>) {
    return this.httpClient
      .post(`${this.baseUrl}/unsubscription`, model)
      .pipe(map((res) => res));
  }

  getPrivacyPolicy(key: string) {
    let params = new HttpParams().set('key', key);
    
    return this.httpClient
      .get<Partial<Unsubscribe>>(`${this.baseUrl}/unsubscription/privacypolicy`, { params })
      .pipe(map((res) => res));
  }
}
