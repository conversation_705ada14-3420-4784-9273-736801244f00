import * as SearchTeeTimePageActions from "../search-teetime/actions/search-teetime.actions";

export interface State {
  isPrepayDeposit: boolean;
}

const initialState: State = {
  isPrepayDeposit: false,
};

export function reducer(
  state = initialState,
  action: SearchTeeTimePageActions.SearchTeeTimePageActionsUnion
): State {
  switch (action.type) {
    case SearchTeeTimePageActions.SearchTeeTimePageActionTypes
      .SelectPrepayDeposit: {
      return {
        ...state,
        isPrepayDeposit: true,
      };
    }
    case SearchTeeTimePageActions.SearchTeeTimePageActionTypes.SearchTeetimes: {
      return {
        ...state,
        isPrepayDeposit: false,
      };
    }
    default: {
      return state;
    }
  }
}

export const getIsPrepayDeposit = (state: State) => state.isPrepayDeposit;
