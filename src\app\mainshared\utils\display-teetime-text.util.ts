import { TranslateService } from '@ngx-translate/core';

export function getDisplayInfoText(
  translate: TranslateService,
  playerNames: string[],
  blockTexts: (string | undefined)[],
  availableSlot: number,
  participantIndex: number,
  reservationOptions: { allowPlayersViewTeeSheetNames: boolean; allowCustomersViewTeeSheetNames: boolean; },
  golferId: number
): string {
  const { allowPlayersViewTeeSheetNames, allowCustomersViewTeeSheetNames } = reservationOptions;

  const availableText = translate.instant('teetime.available');
  const reservedText = translate.instant('teetime.reserved');
  const blockedText = translate.instant('teetime.blocked');
  const walkInText = translate.instant('teetime.walkIn');

  let displayText = availableText;

  if (playerNames.length > 0 && playerNames[participantIndex] === 'WalkIn') return walkInText;

  const blockingText = blockTexts[participantIndex];
  if (blockingText !== undefined) return blockingText || blockedText;

  if (playerNames.length > participantIndex) {
    const currName = playerNames[participantIndex];
    const canViewPlayerName = allowPlayersViewTeeSheetNames && golferId > 0;
    if (canViewPlayerName || allowCustomersViewTeeSheetNames) {
      displayText = currName || availableText;
    } else if (currName && currName !== '-') {
      displayText = reservedText;
    }
  }

  if (displayText === availableText && (availableSlot ?? 0) === 0) {
    displayText = blockedText;
  }

  return displayText;
}