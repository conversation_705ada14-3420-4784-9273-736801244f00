ARG NODE_IMAGE=node:18.20.8
ARG VERSION=6.0-alpine
FROM mcr.microsoft.com/dotnet/aspnet:$VERSION AS base

ARG PATH_BASE=/onlineresweb
ENV PATH_BASE $PATH_BASE

RUN apk update \
    && apk add --no-cache \
    icu-libs

# Set timezone and locale
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false \
    TZ=America/New_York \
    LANG=en_US.UTF-8 \
    LANGUAGE=en_US:en \
    LC_ALL=en_US.UTF-8
RUN apk add --no-cache \
    musl musl-utils musl-locales tzdata
RUN cp /usr/share/zoneinfo/$TZ /etc/localtime
RUN echo "export LC_ALL=${LC_ALL}" >> /etc/profile.d/locale.sh && \
    sed -i "s|LANG=C.UTF-8|LANG=${LANG}|" /etc/profile.d/locale.sh

WORKDIR /app
EXPOSE 80

FROM ${NODE_IMAGE} as node-build
WORKDIR /web
COPY package*.json ./
RUN npm i -g npm@10.8.1
RUN npm install --force
COPY . .

# RUN npm install -g @angular/cli@6.0.5
ARG NODE_ENV=prod
ENV NODE_ENV $NODE_ENV
## Build the angular app in production mode and store the artifacts in dist folder
RUN echo "NODE_ENV: ${NODE_ENV}"
# RUN npm run build --prod --env=$env --build-optimizer
# RUN ng build --configuration=$NODE_ENV
RUN npm run build:$NODE_ENV && npm run post-build

FROM mcr.microsoft.com/dotnet/sdk:$VERSION AS build
WORKDIR /src/server/wwwroot
COPY --from=node-build /web/dist .
WORKDIR /src
COPY server/CPSV4.OnlineReservation.Web.csproj server/
RUN dotnet restore server/CPSV4.OnlineReservation.Web.csproj
COPY . .
WORKDIR /src/server
RUN dotnet build CPSV4.OnlineReservation.Web.csproj -c Release -o /app

FROM build AS publish
RUN dotnet publish CPSV4.OnlineReservation.Web.csproj -c Release -o /app

FROM base AS final
WORKDIR /app
COPY --from=publish /app .
# ENTRYPOINT ["dotnet", "CPSV4.OnlineReservation.Web.dll"]
COPY scripts/docker-entrypoint.sh /
RUN chmod +x /docker-entrypoint.sh
ENTRYPOINT ["/docker-entrypoint.sh"]
CMD ["dotnet", "CPSV4.OnlineReservation.Web.dll"]
