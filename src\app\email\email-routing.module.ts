import { DatePipe } from "@angular/common";
import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { UnsubscribePageComponent } from "./containers/unsubscribe-page/unsubscribe-page.component";

const routes: Routes = [{ path: "Unsubscribe", component: UnsubscribePageComponent }];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
  providers: [DatePipe],
})
export class EmailRoutingModule {
  static components = [UnsubscribePageComponent];
}
