.partner-list-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

::ng-deep .add-partner-button {
  width: 100% !important;
  border: 1px solid #7a9de3;
  border-radius: 20px !important;
  font-weight: 500;
  text-align: left;

  span.mat-button-wrapper {
    display: flex;
    white-space: normal;
    justify-content: space-between;
    padding: 7px;
  }
}

.add-partner-icon {
  display: flex;
  align-items: center;
  transform: scale(1.2);
  color: black;
  float: right;
  margin-right: 10px;
  font-weight: bold;
  flex: none !important;
}

.button-partner {
  width: 100%;
  border-color: var(--border-color);
  border-radius: var(--border-radius);
}

::ng-deep .mat-card.card-partner {
  box-shadow: none !important;
}

:host ::ng-deep .mat-card .mat-card-content {
  padding: 0 !important;
}
