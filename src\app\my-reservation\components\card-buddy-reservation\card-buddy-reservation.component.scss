.buddy-list-display {
  margin-top: 15px;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

::ng-deep .add-buddy-button {
  width: 100% !important;
  border: 1px solid #7a9de3;
  border-radius: 20px !important;
  font-weight: 500;
  text-align: left;
  background-color: white;

  span.mat-button-wrapper {
    display: flex;
    justify-content: space-between;
    padding: 7px;
    white-space: normal;
  }
}

.add-buddy-icon {
  display: flex;
  align-items: center;
  transform: scale(1.2);
  color: black;
  float: right;
  margin-right: 10px;
  font-weight: bold;
  flex: none !important;
}

:host ::ng-deep .mat-form-field-flex {
  padding-left: 10px;
  align-items: center !important;
  background-color: white;
  border-radius: 10px;
}

.search-buddy {
  border-radius: 10px;
}

.icon-display {
  padding-right: 5px;
  transform: scale(1.5);
  color: black;
  font-weight: bold;
}

::ng-deep .mat-option {
  line-height: 1rem !important;
}

::ng-deep .mat-card.card-buddy {
  box-shadow: none !important;
}

:host ::ng-deep .mat-card .mat-card-content {
  padding: 0 !important;
}

.button-buddies {
  width: 100%;
  border-color: var(--border-color);
  border-radius: var(--border-radius);
}

.s-btn {
  line-height: 25px;
}

.removebtn {
  margin-left: 5px;
  height: 35px;
  width: 35px;
  background-color: #fff;
  color: #f00;
  border: 1px solid currentColor;
  border-radius: 2px;
  cursor: pointer;
}
