<mat-card style="padding: 36px !important; max-width: 800px; margin: 0 auto">
  <mat-card-header>
    <mat-card-title class="text-center" style="font-size: x-large; padding-bottom: 16px;">
      {{ "email.emailPreferences" | translate | uppercase }}
    </mat-card-title>
    <mat-card-subtitle class="text-center">
      {{ "email.controlTypeMarketingEmail" | translate }}
      <a href="mailto:<EMAIL>">{{ email }}</a> - {{ "email.simplyCategories" | translate }}.
    </mat-card-subtitle>
  </mat-card-header>

  <mat-card-content>
    <div style="background-color: whitesmoke ; border-radius: 8px; padding: 16px;">
      <mat-checkbox class="mat-checkbox" [(ngModel)]="isReservationEmail">
        <div>
          <p>{{ "email.operationalEmails" | translate }}</p>
        </div>
      </mat-checkbox>
      <div style="margin-left: 24px;">{{ "email.operationalEmailsConfirmation" | translate }}.</div>

      <mat-checkbox class="mat-checkbox" [(ngModel)]="isMarketingEmail">
        <div>
          <label for="marketing-emails"></label>
          <p>{{ "email.marketingEmails" | translate }}
          </p>
        </div>
      </mat-checkbox>
      <div style="margin-left: 24px;">{{ "email.marketingEmailsNewsletters" | translate }}.</div>
    </div>
  </mat-card-content>

  <mat-card-actions class="text-center">
    <button (click)="onClickUnsubscribe()" mat-raised-button color="primary">{{ "form.confirm" | translate }}</button>
  </mat-card-actions>

  <mat-card-footer class="text-center">
    <div style="display: flex; justify-content: center; flex-direction: column;">
      <p [innerHTML]="privacyMessageSanitizedHtml"></p>
      <p style="color: red;">{{ "email.onceUnsubscribed" | translate }}.</p>
    </div>
  </mat-card-footer>


</mat-card>
