import { Injectable } from '@angular/core';
import { OAuthStorage } from 'angular-oauth2-oidc';
import { AppConfigs } from '../constants/appConfig';

@Injectable({ providedIn: 'root' })
export class CustomStorageService extends OAuthStorage {
  private readonly storage: Storage;

  constructor() {
    super();
    this.storage = localStorage;
  }

  getItem(key: string): string {
    return this.storage.getItem(AppConfigs.PREFIX_STORAGE_KEY + key);
  }
  removeItem(key: string): void {
    this.storage.removeItem(AppConfigs.PREFIX_STORAGE_KEY + key);
  }
  setItem(key: string, data: string): void {
    this.storage.setItem(AppConfigs.PREFIX_STORAGE_KEY + key, data);
  }

  clear() {
    this.storage.clear();
  }
}
