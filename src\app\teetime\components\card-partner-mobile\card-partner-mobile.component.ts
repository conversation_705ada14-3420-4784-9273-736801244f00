import { Component, EventEmitter, Input, Output } from "@angular/core";
import { Page } from "../../../shared/models/page.model";
import { PartnerListStatus, Player } from "../../models/player.model";

@Component({
  selector: "app-card-partner-mobile",
  templateUrl: "./card-partner-mobile.component.html",
  styleUrls: ["./card-partner-mobile.component.scss"],
})
export class CardPartnerMobileComponent {
  partnerStatus: PartnerListStatus;
  partnerListAddOrRemove: PartnerListStatus[] = [];

  private _allPlayers: Player[] = [];
  public get allPlayers(): Player[] {
    return this._allPlayers;
  }
  @Input() public set allPlayers(value: Player[]) {
    this._allPlayers = value || [];
  }
  @Input() partnerPage: Page<Player>;
  @Input() isFullPlayer: boolean;
  @Input() isDisplayStore: boolean;
  @Output() loadMorePartners = new EventEmitter();
  @Output() addPartnerPlayer = new EventEmitter<Player>();
  @Output() removePartnerOfPlayer = new EventEmitter<Player>();

  trackByFn(_, item: Player) {
    return item.golferId;
  }

  validateAddPartner(partner: Player) {
    return !this._allPlayers.some((x) => x.golferId === partner.golferId);
  }
}
