import * as fromUnsubscribePage from "./unsubscribe.reducer";
import * as fromRoot from "../../reducers";
import { ActionReducerMap, createFeatureSelector, createSelector } from "@ngrx/store";

export interface UnsubscribeState {
  unsubscribePage: fromUnsubscribePage.State;
}

export interface State extends fromRoot.State {
  unsubscribe: UnsubscribeState;
}

export const reducers: ActionReducerMap<UnsubscribeState, any> = {
  unsubscribePage: fromUnsubscribePage.reducer,
};

// Feature State
export const selectUnsubscribeState = createFeatureSelector<State, UnsubscribeState>(
  "unsubscribe"
);

export const selectUnsubscribePageState = createSelector(
  selectUnsubscribeState,
  (state: UnsubscribeState) => state.unsubscribePage
);

export const selectPrivacyPolicy = createSelector(
  selectUnsubscribePageState,
  fromUnsubscribePage.getPrivacyPolicy
);

export const selectIsMarketingEmail = createSelector(
  selectUnsubscribePageState,
  fromUnsubscribePage.getIsMarketingEmail
);

export const selectIsReservationEmail = createSelector(
  selectUnsubscribePageState,
  fromUnsubscribePage.getIsReservationEmail
);
