<mat-card class="card-buddy">
  <mat-card-content>
    <mat-form-field appearance="fill">
      <mat-icon inline="true" class="icon-display" matPrefix>search</mat-icon>
      <input
        #filter
        type="text"
        placeholder="{{ 'buddy.searchBuddyNames' | translate }}..."
        matInput
        (keyup)="searchBuddies.emit(filter.value)"
        [matAutocomplete]="auto"
        [disabled]="isDisable"
      />
      <mat-autocomplete
        #auto="matAutocomplete"
        (optionSelected)="onSelectSearchBuddy($event)"
        [displayWith]="displayFn"
      >
        <mat-option
          *ngFor="let buddy of filteredBuddies; trackBy: trackByFn"
          [value]="buddy"
        >
          <div mat-line>{{ buddy.firstName }} {{ buddy.lastName }}</div>
          <div mat-line *ngIf="isDisplayStore"> | <small>{{ "buddy.store" | translate }}: {{ buddy.storeName }}</small></div>
        </mat-option>
      </mat-autocomplete>
    </mat-form-field>
    <div class="buddy-list-display">
      <ng-container *ngFor="let buddy of buddiesPage; trackBy: trackByFn">
        <div fxFlex="auto" style="display: flex; align-items: center; width: 100%;">
          <button
          [disabled]="isDisable"
          mat-stroked-button
          class="add-buddy-button"
          (click)="onClickAddBuddy(buddy)"
        >
          <div fxFlex="auto">
            <div>
              <b>{{ buddy.firstName }} {{ buddy.lastName }}</b>
            </div>
            <div *ngIf="isDisplayStore"><small>{{ "buddy.store" | translate }}: {{ buddy.storeName }}</small></div>
          </div>
          <mat-icon *ngIf="!isDisable" fxFlex="10" matSuffix inline="true" class="add-buddy-icon">
            add
          </mat-icon>
        </button>
        <button
            (click)="onClickRemoveBuddy(buddy)"
            class="s-btn removebtn"
            color="warn"
            >
            X
          </button>
        </div>
      </ng-container>
      <button
        *ngIf="isShowBuddiesLoadMore"
        mat-stroked-button
        class="button-buddies"
        (click)="loadMoreBuddies.emit()"
      >
        {{ 'form.loadMore' | translate }}
      </button>
    </div>
  </mat-card-content>
</mat-card>
