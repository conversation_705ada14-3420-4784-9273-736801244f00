import { Injectable } from "@angular/core";

@Injectable({
  providedIn: "root",
})
export class ErrorService {
  public static getValidationErrorMessage(errors: any): string[] {
    const messages: string[] = [];
    for (const field in errors) {
      if (errors.hasOwnProperty(field)) {
        messages.push(...errors[field]);
      }
    }

    return messages;
  }

  public static getErrorMessage(error: any): string {
    let errorMessage = "An unexpected error occurred.";

    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === "string") {
      errorMessage = error;
    } else if (typeof error === "object") {
      errorMessage = error.error_description || error.error || errorMessage;
    }

    return errorMessage;
  }
}
