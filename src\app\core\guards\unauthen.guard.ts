import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, Router } from '@angular/router';
import { OAuthService } from 'angular-oauth2-oidc';

@Injectable({
  providedIn: 'root'
})
export class UnauthenGuard implements CanActivate {

  constructor(private authService: OAuthService, private router: Router) {}

  canActivate(route: ActivatedRouteSnapshot): boolean {
    if (this.authService.hasValidAccessToken()) {
      const returnUrl = route.queryParams["returnUrl"];
      if (returnUrl) {
        const [url, params] = returnUrl.split("?");
        const result = params?.split("&").reduce((res: any, item: string) => {
          const parts = item.split("=");
          res[parts[0]] = parts[1];
          return res;
        }, {});

        this.router.navigate([url], {
          queryParams: result
        });
        return false;
      }
      this.router.navigate(['/']);
      return false;
    }
    return true;
  }
}
