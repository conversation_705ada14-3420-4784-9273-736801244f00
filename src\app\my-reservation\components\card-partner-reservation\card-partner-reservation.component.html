<mat-card class="card-partner">
  <mat-card-content>
    <div class="partner-list-display">
      <ng-container *ngFor="let partner of partnerPage?.items; trackBy: trackByFn" style="align-items: center;">
        <button
          [disabled]="isDisable"
          mat-stroked-button
          class="add-partner-button"
          (click)="addPartnerPlayer.emit(partner)"
        >
          <div fxFlex="auto">
            <div>
              <b>{{ partner.firstName }} {{ partner.lastName }}</b>
            </div>
            <div *ngIf="isDisplayStore"><small>{{ "buddy.store" | translate }}: {{ partner.storeName }}</small></div>
          </div>
          <mat-icon fxFlex="10"
              matSuffix
              inline="true"
              class="add-partner-icon"
              *ngIf="!isDisable"
            >
              add
          </mat-icon>
        </button>
      </ng-container>
      <button
        *ngIf="partnerPage?.currentPage < partnerPage.totalPages"
        mat-stroked-button
        class="button-partner"
        (click)="loadMorePartners.emit()"
      >
        {{ 'form.loadMore' | translate }}
      </button>
    </div>
  </mat-card-content>
</mat-card>
