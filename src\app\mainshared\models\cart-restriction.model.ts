import { ValueObject } from "../../shared-kernal/primitives/value-object";

export interface CartRestriction {
  cartRestrictionId: number;
  fromDate: Date;
  toDate: Date;
  fromTime: Date;
  toTime: Date;
  courseId: number;
  courseName: string;
  days: string[];
  holes: number[];
}

export class CartRestrictionObject extends ValueObject<CartRestriction> {
  static readonly defaultValue  = {
    cartRestrictionId: 0,
    fromDate: new Date(),
    toDate: new Date(),
    fromTime: new Date(),
    toTime: new Date(),
    courseId: 0,
    courseName: '',
    days: [],
    holes: [],
  } as const;

  public static create(value: CartRestriction, defaultValue: Partial<CartRestriction> = null): CartRestriction {
    if (defaultValue && !value) {
      return { ...defaultValue } as CartRestriction;
    }

    const valueTmp: CartRestriction = {
      ...value,
      fromDate: new Date(value.fromDate),
      toDate: new Date(value.toDate),
      fromTime: new Date(value.fromTime),
      toTime: new Date(value.toTime),
    }

    const obj = new CartRestrictionObject(valueTmp, defaultValue ?? this.defaultValue);
    return obj.value;
  }
}
