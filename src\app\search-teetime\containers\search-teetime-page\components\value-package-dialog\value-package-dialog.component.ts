import { Form<PERSON>uilder } from "@angular/forms";
import { FormGroup } from "@angular/forms";
import { Component, Inject, OnInit, EventEmitter } from "@angular/core";
import { Store } from "@ngrx/store";
import { SHItemPrice } from "../../../../../mainshared/models/sh-item-price.model";
import * as fromTeetime from "../../../../reducers";
import { SearchSpinnerDialogComponent } from "../spinner-dialog.component";
import { SelectValuePackage } from "../../../../actions/search-teetime.actions";
import { TeetimePageActions } from "../../../../../teetime/actions";
import { MatDialogRef, MAT_DIALOG_DATA } from "@angular/material/dialog";

@Component({
  // tslint:disable-next-line:component-selector
  selector: "app-value-package-dialog",
  templateUrl: "./value-package-dialog.component.html",
  styleUrls: ["./value-package-dialog.component.scss"],
})
export class SearchValuePackageDialogComponent implements OnInit {
  form: FormGroup;
  isMobile = false;
  component: SearchValuePackageDialogComponent;
  dialogRef: MatDialogRef<SearchSpinnerDialogComponent>;

  constructor(
    private fb: FormBuilder,
    private store: Store<fromTeetime.State>,
    public dialog: MatDialogRef<SearchValuePackageDialogComponent>,
    @Inject(MAT_DIALOG_DATA)
    public data: {
      checkout: EventEmitter<number>;
      teesheetId: number;
      startTime: Date;
      ratePrice: number;
      valuePackageList: SHItemPrice[];
      golferId: number;
      currencyCode: string;
    }
  ) {}

  ngOnInit() {
    this.initForm();
    this.patchObjectToForm();
  }

  onSelectValuePackage(packageCode: string) {
    this.store.dispatch(
      new SelectValuePackage({ valuePackageCode: packageCode })
    );
    this.store.dispatch(
      new TeetimePageActions.SelectValuePackage({
        valuePackageCode: packageCode,
      })
    );
    this.onClickSubmit();
  }

  onClickSubmit() {
    this.dialog.close();
    this.data.checkout.emit(this.data.teesheetId);
  }

  onClickCancel() {
    this.dialog.close();
  }

  private initForm() {
    this.form = this.fb.group({
      checkout: new EventEmitter(),
      teesheetId: 0,
      startTime: null,
      ratePrice: 0,
      valuePackageList: [],
    });
  }

  private patchObjectToForm() {
    if (this.data) {
      this.form.patchValue({ ...this.data });
      this.mapValuePackages();
    }
  }

  private mapValuePackages() {
    if (
      this.data &&
      this.data.valuePackageList &&
      this.data.valuePackageList.length > 0 &&
      this.form
    ) {
      const customerValuePackagesFormGroups = this.data.valuePackageList.map(
        (cd) => this.fb.group({
          ...cd,
          price: cd.isTaxInclusive ? cd.taxInclusivePrice : cd.price,
        })
      );
      const customerValuePackagesFormArray = this.fb.array(
        customerValuePackagesFormGroups
      );
      this.form.setControl(
        "customerValuePackages",
        customerValuePackagesFormArray
      );
    }
  }
}
