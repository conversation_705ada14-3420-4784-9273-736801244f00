ARG NODE_IMAGE=node:18.17.0

FROM ${NODE_IMAGE} AS node-build
WORKDIR /web

# Update system packages and fix curl vulnerability
RUN apt-get update && \
    apt-get install -y \
    curl=7.88.1-10+deb12u4 \
    libcurl3-gnutls=7.88.1-10+deb12u4 \
    libcurl4=7.88.1-10+deb12u4 \
    libcurl4-openssl-dev=7.88.1-10+deb12u4 && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

COPY package*.json ./
RUN npm i -g npm@10.8.1 && \
    npm install --force
COPY . .

RUN npm audit --audit-level=high
