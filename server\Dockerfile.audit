ARG NODE_IMAGE=node:18.20.8

FROM ${NODE_IMAGE} AS node-build
WORKDIR /web

# Update system packages and fix curl vulnerability
RUN apt-get update && \
    apt-get install -y \
    curl \
    libcurl4 \
    libcurl4-openssl-dev && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*
 
COPY package*.json ./
RUN npm i -g npm@10.8.1 && \
    npm install --force
COPY . .

RUN npm audit --audit-level=high
