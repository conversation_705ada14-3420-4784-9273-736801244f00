ARG DOTNET_IMAGE=mcr.microsoft.com/dotnet/aspnet:6.0

FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
WORKDIR /src

# Update system packages and fix curl vulnerability
RUN apt-get update && \
    apt-get install -y \
    curl \
    libcurl4 \
    libcurl4-openssl-dev && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Copy csproj and restore dependencies
COPY *.csproj ./
RUN dotnet restore

# Copy everything else and build
COPY . .
RUN dotnet build -c Release -o /app/build && \
    dotnet list package --vulnerable --include-transitive
