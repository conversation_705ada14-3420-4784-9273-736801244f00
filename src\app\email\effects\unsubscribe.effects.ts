import { Injectable } from "@angular/core";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import { catchError, map, mergeMap, switchMap, tap } from "rxjs/operators";
import { CoreActions, SpinnerProgress } from "../../core/actions";
import { of } from "rxjs";
import { Store } from "@ngrx/store";
import { UnsubscribeService } from "../services/unsubscribe.service";
import * as UnsubscribePageActions from "../actions/unsubscribe.actions";
import { TranslateService } from "@ngx-translate/core";

const showSpinnerProgressActions = [
  UnsubscribePageActions.UnsubscribeActionTypes.FetchUnsubscribeInformation,
  UnsubscribePageActions.UnsubscribeActionTypes.Unsubscribe,
];

const hideSpinnerProgressActions = [
  UnsubscribePageActions.UnsubscribeActionTypes.UnsubscribeSuccess,
  UnsubscribePageActions.UnsubscribeActionTypes.UnsubscribeFailed,
  UnsubscribePageActions.UnsubscribeActionTypes.FetchUnsubscribeInformationSuccess,
  UnsubscribePageActions.UnsubscribeActionTypes.FetchUnsubscribeInformationFailed,
];

@Injectable()
export class UnsubscribeEffects {
  showSpinner = createEffect(() =>
    this.actions$.pipe(
      ofType(...showSpinnerProgressActions),
      map(() => new SpinnerProgress.ShowSpinner())
    )
  );

  hideSpinner = createEffect(() =>
    this.actions$.pipe(
      ofType(...hideSpinnerProgressActions),
      map(() => new SpinnerProgress.HideSpinner())
    )
  );

  fetchUnsubscribeInformation$ = createEffect(() =>
    this.actions$.pipe(
      ofType<UnsubscribePageActions.FetchUnsubscribeInformation>(
        UnsubscribePageActions.UnsubscribeActionTypes.FetchUnsubscribeInformation
      ),
      map((action) => action.payload),
      mergeMap((model) =>
        this.unsubscribeService.getPrivacyPolicy(model.key).pipe(
          switchMap((res) => [
            new UnsubscribePageActions.FetchUnsubscribeInformationSuccess(res),
          ]),
          catchError((error) =>
            of(new UnsubscribePageActions.FetchUnsubscribeInformationFailed(error))
          )
        )
      )
    )
  );

  unsubscribe$ = createEffect(() =>
    this.actions$.pipe(
      ofType<UnsubscribePageActions.Unsubscribe>(
        UnsubscribePageActions.UnsubscribeActionTypes.Unsubscribe
      ),
      map((action) => action.payload),
      mergeMap((model) =>
        this.unsubscribeService.unsubscribe(model).pipe(
          switchMap((res) => [
            new UnsubscribePageActions.UnsubscribeSuccess(res),
          ]),
          catchError((error) =>
            of(new UnsubscribePageActions.UnsubscribeFailed(error))
          )
        )
      )
    )
  );

  unsubscribeSuccess$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType<UnsubscribePageActions.UnsubscribeSuccess>(
          UnsubscribePageActions.UnsubscribeActionTypes.UnsubscribeSuccess
        ),
        tap(() => {
          this.store.dispatch(
            new CoreActions.ShowModalMessage({
              details: this.translate.instant("email.preferencesUpdated"),
            })
          );
        })
      ),
    { dispatch: false }
  );

  constructor(
    private actions$: Actions,
    private store: Store<{}>,
    private unsubscribeService: UnsubscribeService,
    private translate: TranslateService,
  ) {}
}
