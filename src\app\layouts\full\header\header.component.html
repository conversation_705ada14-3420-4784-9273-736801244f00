<!-- ============================================================== -->
<!-- Profile - style you can find in header.scss -->
<!-- ============================================================== -->
<div *ngIf="(loggedIn$ | async) && (isGuest$ | async) === false">
  <button
    [matMenuTriggerFor]="profile"
    mat-icon-button
    class="m-r-5"
    *ngIf="isMobile; else displayName"
  >
    <mat-icon>menu</mat-icon>
  </button>
  <ng-template #displayName>
    <button
      [matMenuTriggerFor]="profile"
      mat-stroked-button
      class="mat-button"
      style="float: right; color: #2961d5; background-color: #fff"
    >
      <!-- <span class="mat-button-wrapper"> {{ fullname$ | async }} </span> -->
      <span class="mat-button-wrapper">
        {{ "header.myAccount" | translate }}
      </span>
      <div class="mat-button-ripple mat-ripple" matripple=""></div>
      <div class="mat-button-focus-overlay"></div>
    </button>
  </ng-template>
  <mat-menu #profile="matMenu" class="mymegamenu">
    <button *ngIf="isMobile" mat-menu-item>
      <mat-icon>person_outline</mat-icon> {{ fullname$ | async }}
    </button>
    <button mat-menu-item (click)="onClickTeeTime()">
      <mat-icon>golf_course</mat-icon> {{ "header.teetime" | translate }}
    </button>
    <button mat-menu-item (click)="onClickProfile()">
      <mat-icon>people</mat-icon> {{ "header.profile" | translate }}
    </button>
    <button mat-menu-item (click)="onClickMyReservation()">
      <mat-icon>today</mat-icon> {{ "header.reservations" | translate }}
    </button>
    <button mat-menu-item (click)="onClickSignOut()">
      <mat-icon>exit_to_app</mat-icon> {{ "header.signOut" | translate }}
    </button>
  </mat-menu>
</div>

<div *ngIf="(loggedIn$ | async) && (isGuest$ | async)">
  <button
    [matMenuTriggerFor]="profileGuest"
    mat-icon-button
    class="m-r-5"
    *ngIf="isMobile; else displayNameGuest"
  >
    <mat-icon>menu</mat-icon>
  </button>
  <ng-template #displayNameGuest>
    <button [matMenuTriggerFor]="profileGuest" mat-button style="float: right">
      {{ fullname$ | async }} ({{ "guestForm.guest" | translate }})
    </button>
  </ng-template>
  <mat-menu #profileGuest="matMenu" class="mymegamenu">
    <button *ngIf="isMobile" mat-menu-item>
      <mat-icon>person_outline</mat-icon> {{ fullname$ | async }} ({{
        "guestForm.guest" | translate
      }})
    </button>
    <button mat-menu-item (click)="onClickTeeTime()">
      <mat-icon>golf_course</mat-icon> {{ "header.teetime" | translate }}
    </button>
    <button mat-menu-item (click)="onClickSignOut()">
      <mat-icon>exit_to_app</mat-icon> {{ "header.signOut" | translate }}
    </button>
  </mat-menu>
</div>

<button
  *ngIf="(loggedIn$ | async) === false"
  aria-haspopup="true"
  mat-button=""
  class="mat-button event-wrapper"
  (click)="onClickSingIn()"
>
  <span class="mat-button-wrapper"> {{ "header.signIn" | translate }} </span>
  <div class="mat-button-ripple mat-ripple" matripple=""></div>
  <div class="mat-button-focus-overlay"></div>
</button>
